# Blackjack Game

A complete command-line blackjack game implemented in Java.

## Features

- **Complete Blackjack Rules**: Follows standard blackjack rules with proper card values
- **Ace Handling**: Aces automatically adjust between 1 and 11 for optimal hand value
- **Blackjack Payouts**: 3:2 payout for natural blackjacks
- **Dealer AI**: Dealer follows standard rules (hits on 16, stands on 17)
- **Betting System**: Start with $1000 and place bets each round
- **Continuous Play**: Play multiple rounds until you run out of money or choose to quit

## How to Play

### Option 1: Using IDE (Recommended)
1. **Open the project** in your Java IDE (IntelliJ IDEA, Eclipse, VS Code, etc.)
2. **Navigate to** `src/BlackjackGame.java`
3. **Click the green play button** ▶️ next to the `main` method or class
4. **The game will start** in the IDE's console/terminal

### Option 2: Command Line
1. **Compile the Java files**:
   ```bash
   javac src/*.java
   ```
2. **Run the game**:
   ```bash
   java -cp src BlackjackGame
   ```

2. **Place your bet**: Enter a bet amount between $1 and your current balance

3. **Play your hand**:
   - Type `h` or `hit` to take another card
   - Type `s` or `stand` to keep your current hand

4. **Win conditions**:
   - Get closer to 21 than the dealer without going over
   - Get a blackjack (21 with 2 cards) for a 3:2 payout
   - Dealer busts (goes over 21)

## Game Rules

### Card Values
- **Number Cards (2-10)**: Face value
- **Face Cards (Jack, Queen, King)**: Worth 10 points
- **Ace**: Worth 1 or 11 points (automatically optimized)

### Gameplay
1. Both player and dealer receive 2 cards initially
2. Player's cards are face up, dealer has one card hidden
3. Player acts first - can hit or stand
4. If player doesn't bust, dealer plays (must hit on 16, stand on 17)
5. Highest hand without busting wins

### Special Situations
- **Blackjack**: 21 with first 2 cards (Ace + 10-value card) pays 3:2
- **Push**: Tie game, bet is returned
- **Bust**: Going over 21 results in automatic loss

## Example Gameplay

```
Welcome to Blackjack!
Try to get as close to 21 as possible without going over.
Face cards are worth 10, Aces are worth 1 or 11.
Blackjack (21 with 2 cards) pays 3:2!

Your balance: $1000
Place your bet (1-1000): $50

Your hand: 7 of Hearts, King of Spades (Value: 17)
Dealer's hand: Ace of Clubs, [Hidden Card]

Do you want to (h)it or (s)tand? s

Dealer's turn:
Dealer's hand: Ace of Clubs, 6 of Diamonds (Value: 17)

You win $50!
Your balance: $1050
```

## File Structure

```
src/
├── BlackjackGame.java  # Main game class with game loop (click ▶️ here to run!)
├── Card.java          # Represents individual playing cards
├── Deck.java          # Manages the deck of cards
└── Hand.java          # Manages a hand of cards
```

## Requirements

- Java 8 or higher
- Any Java IDE (IntelliJ IDEA, Eclipse, VS Code with Java extension)
- No external dependencies required

## Quick Start for IDE Users

1. **Open your Java IDE**
2. **Import/Open the project folder**
3. **Navigate to `src/BlackjackGame.java`**
4. **Look for the `main` method**
5. **Click the green play button ▶️** next to it
6. **Start playing in the console!**

Enjoy playing blackjack!
