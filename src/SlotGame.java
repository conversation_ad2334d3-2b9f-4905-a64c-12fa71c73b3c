import java.util.Random;
import java.util.Scanner;

/**
 * Slot machine game with 3 reels and various winning combinations.
 */
public class SlotGame {
    private int playerBalance;
    private int currentBet;
    private Scanner scanner;
    private Random random;
    
    // Slot symbols
    private static final String[] SYMBOLS = {"7", "BAR", "CHERRY", "LEMON", "BELL", "STAR"};
    private static final String JACKPOT_SYMBOL = "7";
    
    public SlotGame(int startingBalance) {
        this.playerBalance = startingBalance;
        this.currentBet = 0;
        this.scanner = new Scanner(System.in);
        this.random = new Random();
    }
    
    /**
     * Allow player to place a bet.
     * @return true if bet was placed successfully
     */
    private boolean placeBet() {
        System.out.println("\n$ Your balance: $" + playerBalance);
        
        if (playerBalance <= 0) {
            System.out.println("X You're out of money! Game over.");
            return false;
        }
        
        while (true) {
            try {
                System.out.print("$ Place your bet (1-" + Math.min(playerBalance, 100) + "): $");
                int bet = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                if (bet >= 1 && bet <= playerBalance && bet <= 100) {
                    currentBet = bet;
                    System.out.println("+ Bet placed: $" + bet);
                    return true;
                } else if (bet > 100) {
                    System.out.println("- Maximum bet is $100");
                } else {
                    System.out.println("- Please bet between $1 and $" + Math.min(playerBalance, 100));
                }
            } catch (Exception e) {
                System.out.println("- Please enter a valid number.");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }
    
    /**
     * Spin the slot machine reels.
     * @return Array of 3 symbols
     */
    private String[] spinReels() {
        String[] result = new String[3];
        for (int i = 0; i < 3; i++) {
            result[i] = SYMBOLS[random.nextInt(SYMBOLS.length)];
        }
        return result;
    }
    
    /**
     * Display the slot machine with spinning animation.
     * @param finalResult The final result to display
     */
    private void displaySlotMachine(String[] finalResult) {
        System.out.println("\n" + repeatString("=", 40));
        System.out.println("*        SLOT MACHINE SPINNING...       *");
        System.out.println(repeatString("=", 40));
        
        // Spinning animation
        for (int spin = 0; spin < 5; spin++) {
            String[] tempResult = spinReels();
            System.out.print("\r| " + tempResult[0] + " | " + tempResult[1] + " | " + tempResult[2] + " |");
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // Final result
        System.out.print("\r| " + finalResult[0] + " | " + finalResult[1] + " | " + finalResult[2] + " |");
        System.out.println("\n" + repeatString("=", 40));
    }
    
    /**
     * Calculate winnings based on the slot result.
     * @param result The slot machine result
     * @return Winnings multiplier
     */
    private int calculateWinnings(String[] result) {
        // Check for three of a kind
        if (result[0].equals(result[1]) && result[1].equals(result[2])) {
            if (result[0].equals(JACKPOT_SYMBOL)) {
                return 50; // Jackpot: 50x bet
            } else if (result[0].equals("BAR")) {
                return 20; // Triple BAR: 20x bet
            } else if (result[0].equals("BELL")) {
                return 15; // Triple BELL: 15x bet
            } else if (result[0].equals("STAR")) {
                return 10; // Triple STAR: 10x bet
            } else if (result[0].equals("CHERRY")) {
                return 8; // Triple CHERRY: 8x bet
            } else if (result[0].equals("LEMON")) {
                return 5; // Triple LEMON: 5x bet
            }
        }
        
        // Check for two of a kind
        if (result[0].equals(result[1]) || result[1].equals(result[2]) || result[0].equals(result[2])) {
            if (containsSymbol(result, JACKPOT_SYMBOL)) {
                return 5; // Two 7s: 5x bet
            } else if (containsSymbol(result, "BAR")) {
                return 3; // Two BARs: 3x bet
            } else if (containsSymbol(result, "CHERRY")) {
                return 2; // Two CHERRYs: 2x bet
            }
        }
        
        // Check for any CHERRY (consolation prize)
        if (containsSymbol(result, "CHERRY")) {
            return 1; // Any CHERRY: 1x bet (break even)
        }
        
        return 0; // No win
    }
    
    /**
     * Check if result contains a specific symbol.
     * @param result The slot result
     * @param symbol The symbol to check for
     * @return true if symbol is found
     */
    private boolean containsSymbol(String[] result, String symbol) {
        for (String s : result) {
            if (s.equals(symbol)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Display the payout table.
     */
    private void showPayoutTable() {
        System.out.println("\n" + repeatString("=", 50));
        System.out.println("*                PAYOUT TABLE                *");
        System.out.println(repeatString("=", 50));
        System.out.println("* Triple 7s (JACKPOT)     -> 50x your bet   *");
        System.out.println("* Triple BARs             -> 20x your bet   *");
        System.out.println("* Triple BELLs            -> 15x your bet   *");
        System.out.println("* Triple STARs            -> 10x your bet   *");
        System.out.println("* Triple CHERRYs          -> 8x your bet    *");
        System.out.println("* Triple LEMONs           -> 5x your bet    *");
        System.out.println("* Two 7s                  -> 5x your bet    *");
        System.out.println("* Two BARs                -> 3x your bet    *");
        System.out.println("* Two CHERRYs             -> 2x your bet    *");
        System.out.println("* Any CHERRY              -> 1x your bet    *");
        System.out.println(repeatString("=", 50));
    }
    
    /**
     * Play one round of slots.
     * @return true to continue playing
     */
    private boolean playRound() {
        // Place bet
        if (!placeBet()) {
            return false;
        }
        
        // Spin the reels
        String[] result = spinReels();
        displaySlotMachine(result);
        
        // Calculate winnings
        int multiplier = calculateWinnings(result);
        int winnings = currentBet * multiplier;
        
        if (multiplier > 0) {
            playerBalance += winnings - currentBet; // Subtract original bet, add winnings
            if (multiplier >= 50) {
                System.out.println("*** JACKPOT!!! You won $" + winnings + "! ***");
            } else if (multiplier >= 10) {
                System.out.println("*** BIG WIN! You won $" + winnings + "! ***");
            } else {
                System.out.println("*** You won $" + winnings + "! ***");
            }
        } else {
            playerBalance -= currentBet;
            System.out.println("- Sorry, you lost $" + currentBet + ". Better luck next time!");
        }
        
        System.out.println("Your balance: $" + playerBalance);
        
        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("You're out of money! Thanks for playing!");
            return false;
        }
        
        while (true) {
            System.out.print("\nDo you want to (s)pin again, see (p)ayout table, or (q)uit? ");
            String choice = scanner.nextLine().toLowerCase().trim();
            if (choice.equals("s") || choice.equals("spin")) {
                return true;
            } else if (choice.equals("p") || choice.equals("payout")) {
                showPayoutTable();
                // Continue the loop to ask again
            } else if (choice.equals("q") || choice.equals("quit")) {
                return false;
            } else {
                System.out.println("Please enter 's' for spin, 'p' for payout table, or 'q' to quit.");
            }
        }
    }
    
    /**
     * Helper method to repeat a string.
     * @param str The string to repeat
     * @param count Number of times to repeat
     * @return Repeated string
     */
    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * Main game loop for slots.
     */
    public void play() {
        System.out.println("*** WELCOME TO THE SLOT MACHINE! ***");
        System.out.println("* Try your luck with our 3-reel slot machine!");
        System.out.println("* Starting balance: $" + playerBalance);
        System.out.println("* Maximum bet per spin: $100");
        System.out.println(repeatString("=", 50));
        
        showPayoutTable();
        
        while (playRound()) {
            // Continue playing rounds
        }
        
        System.out.println("\n*** Thanks for playing slots! Final balance: $" + playerBalance + " ***");
    }
    
    /**
     * Get the current player balance.
     * @return Current balance
     */
    public int getBalance() {
        return playerBalance;
    }
}
