import random
from typing import List, Tuple

class Card:
    """Represents a playing card."""
    
    def __init__(self, suit: str, rank: str):
        self.suit = suit
        self.rank = rank
    
    def __str__(self):
        return f"{self.rank} of {self.suit}"
    
    def get_value(self) -> int:
        """Get the blackjack value of the card."""
        if self.rank in ['Jack', 'Queen', 'King']:
            return 10
        elif self.rank == 'Ace':
            return 11  # Will be adjusted in hand calculation
        else:
            return int(self.rank)

class Deck:
    """Represents a deck of playing cards."""
    
    def __init__(self):
        self.cards = []
        self.create_deck()
        self.shuffle()
    
    def create_deck(self):
        """Create a standard 52-card deck."""
        suits = ['Hearts', 'Diamonds', 'Clubs', 'Spades']
        ranks = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'Jack', 'Queen', 'King', 'Ace']
        
        for suit in suits:
            for rank in ranks:
                self.cards.append(Card(suit, rank))
    
    def shuffle(self):
        """Shuffle the deck."""
        random.shuffle(self.cards)
    
    def deal_card(self) -> Card:
        """Deal one card from the deck."""
        if not self.cards:
            # If deck is empty, create and shuffle a new one
            self.create_deck()
            self.shuffle()
        return self.cards.pop()

class Hand:
    """Represents a hand of cards."""
    
    def __init__(self):
        self.cards: List[Card] = []
    
    def add_card(self, card: Card):
        """Add a card to the hand."""
        self.cards.append(card)
    
    def get_value(self) -> int:
        """Calculate the best possible value of the hand."""
        value = 0
        aces = 0
        
        for card in self.cards:
            if card.rank == 'Ace':
                aces += 1
                value += 11
            else:
                value += card.get_value()
        
        # Adjust for aces
        while value > 21 and aces > 0:
            value -= 10
            aces -= 1
        
        return value
    
    def is_blackjack(self) -> bool:
        """Check if hand is a blackjack (21 with 2 cards)."""
        return len(self.cards) == 2 and self.get_value() == 21
    
    def is_bust(self) -> bool:
        """Check if hand is bust (over 21)."""
        return self.get_value() > 21
    
    def __str__(self):
        """String representation of the hand."""
        cards_str = ", ".join(str(card) for card in self.cards)
        return f"{cards_str} (Value: {self.get_value()})"

class BlackjackGame:
    """Main blackjack game class."""
    
    def __init__(self):
        self.deck = Deck()
        self.player_hand = Hand()
        self.dealer_hand = Hand()
        self.player_balance = 1000
        self.current_bet = 0
    
    def reset_hands(self):
        """Reset hands for a new game."""
        self.player_hand = Hand()
        self.dealer_hand = Hand()
    
    def place_bet(self) -> bool:
        """Allow player to place a bet."""
        print(f"\nYour balance: ${self.player_balance}")
        
        if self.player_balance <= 0:
            print("You're out of money! Game over.")
            return False
        
        while True:
            try:
                bet = int(input(f"Place your bet (1-{self.player_balance}): $"))
                if 1 <= bet <= self.player_balance:
                    self.current_bet = bet
                    return True
                else:
                    print(f"Please bet between $1 and ${self.player_balance}")
            except ValueError:
                print("Please enter a valid number.")
    
    def deal_initial_cards(self):
        """Deal initial two cards to player and dealer."""
        # Deal two cards to player
        self.player_hand.add_card(self.deck.deal_card())
        self.player_hand.add_card(self.deck.deal_card())
        
        # Deal two cards to dealer
        self.dealer_hand.add_card(self.deck.deal_card())
        self.dealer_hand.add_card(self.deck.deal_card())
    
    def show_hands(self, hide_dealer_card: bool = True):
        """Display current hands."""
        print(f"\nYour hand: {self.player_hand}")
        
        if hide_dealer_card:
            print(f"Dealer's hand: {self.dealer_hand.cards[0]}, [Hidden Card]")
        else:
            print(f"Dealer's hand: {self.dealer_hand}")
    
    def player_turn(self) -> bool:
        """Handle player's turn. Returns True if player didn't bust."""
        while True:
            self.show_hands()
            
            if self.player_hand.is_bust():
                print("\nYou busted! You lose.")
                return False
            
            if self.player_hand.get_value() == 21:
                print("\nYou have 21!")
                break
            
            action = input("\nDo you want to (h)it or (s)tand? ").lower().strip()
            
            if action == 'h' or action == 'hit':
                self.player_hand.add_card(self.deck.deal_card())
                print(f"You drew: {self.player_hand.cards[-1]}")
            elif action == 's' or action == 'stand':
                break
            else:
                print("Please enter 'h' for hit or 's' for stand.")
        
        return not self.player_hand.is_bust()
    
    def dealer_turn(self):
        """Handle dealer's turn."""
        print("\nDealer's turn:")
        self.show_hands(hide_dealer_card=False)
        
        while self.dealer_hand.get_value() < 17:
            card = self.deck.deal_card()
            self.dealer_hand.add_card(card)
            print(f"Dealer draws: {card}")
            print(f"Dealer's hand: {self.dealer_hand}")
        
        if self.dealer_hand.is_bust():
            print("Dealer busted!")
    
    def determine_winner(self) -> str:
        """Determine the winner and return result."""
        player_value = self.player_hand.get_value()
        dealer_value = self.dealer_hand.get_value()
        
        # Check for blackjacks
        player_blackjack = self.player_hand.is_blackjack()
        dealer_blackjack = self.dealer_hand.is_blackjack()
        
        if player_blackjack and dealer_blackjack:
            return "push_blackjack"
        elif player_blackjack:
            return "player_blackjack"
        elif dealer_blackjack:
            return "dealer_blackjack"
        
        # Check for busts
        if self.player_hand.is_bust():
            return "player_bust"
        elif self.dealer_hand.is_bust():
            return "dealer_bust"
        
        # Compare values
        if player_value > dealer_value:
            return "player_wins"
        elif dealer_value > player_value:
            return "dealer_wins"
        else:
            return "push"
    
    def handle_payout(self, result: str):
        """Handle betting payouts based on game result."""
        if result == "player_blackjack":
            payout = int(self.current_bet * 1.5)  # 3:2 payout for blackjack
            self.player_balance += payout
            print(f"Blackjack! You win ${payout}!")
        elif result in ["player_wins", "dealer_bust"]:
            self.player_balance += self.current_bet
            print(f"You win ${self.current_bet}!")
        elif result in ["push", "push_blackjack"]:
            print("It's a push! Your bet is returned.")
        else:  # Player loses
            self.player_balance -= self.current_bet
            print(f"You lose ${self.current_bet}.")
    
    def play_round(self) -> bool:
        """Play one round of blackjack. Returns True to continue playing."""
        self.reset_hands()
        
        # Place bet
        if not self.place_bet():
            return False
        
        # Deal initial cards
        self.deal_initial_cards()
        
        # Check for initial blackjacks
        if self.player_hand.is_blackjack() or self.dealer_hand.is_blackjack():
            self.show_hands(hide_dealer_card=False)
            result = self.determine_winner()
            self.handle_payout(result)
        else:
            # Player's turn
            if self.player_turn():
                # Dealer's turn (only if player didn't bust)
                self.dealer_turn()
            
            # Determine winner and handle payout
            result = self.determine_winner()
            self.handle_payout(result)
        
        print(f"Your balance: ${self.player_balance}")
        
        # Ask if player wants to continue
        if self.player_balance <= 0:
            print("You're out of money! Thanks for playing!")
            return False
        
        while True:
            play_again = input("\nDo you want to play another round? (y/n): ").lower().strip()
            if play_again in ['y', 'yes']:
                return True
            elif play_again in ['n', 'no']:
                return False
            else:
                print("Please enter 'y' for yes or 'n' for no.")
    
    def play(self):
        """Main game loop."""
        print("Welcome to Blackjack!")
        print("Try to get as close to 21 as possible without going over.")
        print("Face cards are worth 10, Aces are worth 1 or 11.")
        print("Blackjack (21 with 2 cards) pays 3:2!")
        
        while self.play_round():
            pass
        
        print(f"\nThanks for playing! Final balance: ${self.player_balance}")

def main():
    """Main function to start the game."""
    game = BlackjackGame()
    game.play()

if __name__ == "__main__":
    main()
