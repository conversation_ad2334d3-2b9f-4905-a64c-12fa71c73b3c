import java.util.Scanner;

/**
 * Main blackjack game class with complete game logic.
 */
public class BlackjackGame {
    private Deck deck;
    private Hand playerHand;
    private Hand dealerHand;
    private int playerBalance;
    private int currentBet;
    private Scanner scanner;
    
    public BlackjackGame() {
        this.deck = new Deck();
        this.playerHand = new Hand();
        this.dealerHand = new Hand();
        this.playerBalance = 1000;
        this.currentBet = 0;
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Reset hands for a new game.
     */
    private void resetHands() {
        playerHand.clear();
        dealerHand.clear();
    }
    
    /**
     * Allow player to place a bet.
     * @return true if bet was placed successfully
     */
    private boolean placeBet() {
        System.out.println("\nYour balance: $" + playerBalance);
        
        if (playerBalance <= 0) {
            System.out.println("You're out of money! Game over.");
            return false;
        }
        
        while (true) {
            try {
                System.out.print("Place your bet (1-" + playerBalance + "): $");
                int bet = scanner.nextInt();
                scanner.nextLine(); // Consume newline
                
                if (bet >= 1 && bet <= playerBalance) {
                    currentBet = bet;
                    return true;
                } else {
                    System.out.println("Please bet between $1 and $" + playerBalance);
                }
            } catch (Exception e) {
                System.out.println("Please enter a valid number.");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }
    
    /**
     * Deal initial two cards to player and dealer.
     */
    private void dealInitialCards() {
        // Deal two cards to player
        playerHand.addCard(deck.dealCard());
        playerHand.addCard(deck.dealCard());
        
        // Deal two cards to dealer
        dealerHand.addCard(deck.dealCard());
        dealerHand.addCard(deck.dealCard());
    }
    
    /**
     * Display current hands.
     * @param hideDealerCard whether to hide dealer's second card
     */
    private void showHands(boolean hideDealerCard) {
        System.out.println("\nYour hand: " + playerHand);
        
        if (hideDealerCard && dealerHand.size() >= 2) {
            System.out.println("Dealer's hand: " + dealerHand.getCards().get(0) + ", [Hidden Card]");
        } else {
            System.out.println("Dealer's hand: " + dealerHand);
        }
    }
    
    /**
     * Handle player's turn.
     * @return true if player didn't bust
     */
    private boolean playerTurn() {
        while (true) {
            showHands(true);
            
            if (playerHand.isBust()) {
                System.out.println("\nYou busted! You lose.");
                return false;
            }
            
            if (playerHand.getValue() == 21) {
                System.out.println("\nYou have 21!");
                break;
            }
            
            System.out.print("\nDo you want to (h)it or (s)tand? ");
            String action = scanner.nextLine().toLowerCase().trim();
            
            if (action.equals("h") || action.equals("hit")) {
                Card newCard = deck.dealCard();
                playerHand.addCard(newCard);
                System.out.println("You drew: " + newCard);
            } else if (action.equals("s") || action.equals("stand")) {
                break;
            } else {
                System.out.println("Please enter 'h' for hit or 's' for stand.");
            }
        }
        
        return !playerHand.isBust();
    }
    
    /**
     * Handle dealer's turn.
     */
    private void dealerTurn() {
        System.out.println("\nDealer's turn:");
        showHands(false);
        
        while (dealerHand.getValue() < 17) {
            Card card = deck.dealCard();
            dealerHand.addCard(card);
            System.out.println("Dealer draws: " + card);
            System.out.println("Dealer's hand: " + dealerHand);
        }
        
        if (dealerHand.isBust()) {
            System.out.println("Dealer busted!");
        }
    }
    
    /**
     * Determine the winner and return result.
     * @return String representing the game result
     */
    private String determineWinner() {
        int playerValue = playerHand.getValue();
        int dealerValue = dealerHand.getValue();
        
        // Check for blackjacks
        boolean playerBlackjack = playerHand.isBlackjack();
        boolean dealerBlackjack = dealerHand.isBlackjack();
        
        if (playerBlackjack && dealerBlackjack) {
            return "push_blackjack";
        } else if (playerBlackjack) {
            return "player_blackjack";
        } else if (dealerBlackjack) {
            return "dealer_blackjack";
        }
        
        // Check for busts
        if (playerHand.isBust()) {
            return "player_bust";
        } else if (dealerHand.isBust()) {
            return "dealer_bust";
        }
        
        // Compare values
        if (playerValue > dealerValue) {
            return "player_wins";
        } else if (dealerValue > playerValue) {
            return "dealer_wins";
        } else {
            return "push";
        }
    }
    
    /**
     * Handle betting payouts based on game result.
     * @param result The game result
     */
    private void handlePayout(String result) {
        switch (result) {
            case "player_blackjack":
                int payout = (int) (currentBet * 1.5); // 3:2 payout for blackjack
                playerBalance += payout;
                System.out.println("Blackjack! You win $" + payout + "!");
                break;
            case "player_wins":
            case "dealer_bust":
                playerBalance += currentBet;
                System.out.println("You win $" + currentBet + "!");
                break;
            case "push":
            case "push_blackjack":
                System.out.println("It's a push! Your bet is returned.");
                break;
            default: // Player loses
                playerBalance -= currentBet;
                System.out.println("You lose $" + currentBet + ".");
                break;
        }
    }
    
    /**
     * Play one round of blackjack.
     * @return true to continue playing
     */
    private boolean playRound() {
        resetHands();
        
        // Place bet
        if (!placeBet()) {
            return false;
        }
        
        // Deal initial cards
        dealInitialCards();
        
        // Check for initial blackjacks
        if (playerHand.isBlackjack() || dealerHand.isBlackjack()) {
            showHands(false);
            String result = determineWinner();
            handlePayout(result);
        } else {
            // Player's turn
            if (playerTurn()) {
                // Dealer's turn (only if player didn't bust)
                dealerTurn();
            }
            
            // Determine winner and handle payout
            String result = determineWinner();
            handlePayout(result);
        }
        
        System.out.println("Your balance: $" + playerBalance);
        
        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("You're out of money! Thanks for playing!");
            return false;
        }
        
        while (true) {
            System.out.print("\nDo you want to play another round? (y/n): ");
            String playAgain = scanner.nextLine().toLowerCase().trim();
            if (playAgain.equals("y") || playAgain.equals("yes")) {
                return true;
            } else if (playAgain.equals("n") || playAgain.equals("no")) {
                return false;
            } else {
                System.out.println("Please enter 'y' for yes or 'n' for no.");
            }
        }
    }
    
    /**
     * Main game loop.
     */
    public void play() {
        System.out.println("Welcome to Blackjack!");
        System.out.println("Try to get as close to 21 as possible without going over.");
        System.out.println("Face cards are worth 10, Aces are worth 1 or 11.");
        System.out.println("Blackjack (21 with 2 cards) pays 3:2!");
        
        while (playRound()) {
            // Continue playing rounds
        }
        
        System.out.println("\nThanks for playing! Final balance: $" + playerBalance);
        scanner.close();
    }
    
    /**
     * Main method to start the game.
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        BlackjackGame game = new BlackjackGame();
        game.play();
    }
}
