import java.util.Scanner;

/**
 * Main blackjack game class with complete game logic.
 */
public class BlackjackGame {
    private Deck deck;
    private Hand playerHand;
    private Hand dealerHand;
    private int playerBalance;
    private int currentBet;
    private Scanner scanner;
    
    public BlackjackGame() {
        this.deck = new Deck();
        this.playerHand = new Hand();
        this.dealerHand = new Hand();
        this.playerBalance = 1000;
        this.currentBet = 0;
        this.scanner = new Scanner(System.in);
    }
    
    /**
     * Reset hands for a new game.
     */
    private void resetHands() {
        playerHand.clear();
        dealerHand.clear();
    }
    
    /**
     * Allow player to place a bet.
     * @return true if bet was placed successfully
     */
    private boolean placeBet() {
        System.out.println("\n💰 Your balance: $" + playerBalance);

        if (playerBalance <= 0) {
            System.out.println("💸 You're out of money! Game over.");
            return false;
        }

        while (true) {
            try {
                System.out.print("💵 Place your bet (1-" + playerBalance + "): $");
                int bet = scanner.nextInt();
                scanner.nextLine(); // Consume newline

                if (bet >= 1 && bet <= playerBalance) {
                    currentBet = bet;
                    System.out.println("✅ Bet placed: $" + bet);
                    return true;
                } else {
                    System.out.println("❌ Please bet between $1 and $" + playerBalance);
                }
            } catch (Exception e) {
                System.out.println("❌ Please enter a valid number.");
                scanner.nextLine(); // Clear invalid input
            }
        }
    }
    
    /**
     * Deal initial two cards to player and dealer.
     */
    private void dealInitialCards() {
        // Deal two cards to player
        playerHand.addCard(deck.dealCard());
        playerHand.addCard(deck.dealCard());
        
        // Deal two cards to dealer
        dealerHand.addCard(deck.dealCard());
        dealerHand.addCard(deck.dealCard());
    }
    
    /**
     * Display current hands with enhanced formatting.
     * @param hideDealerCard whether to hide dealer's second card
     */
    private void showHands(boolean hideDealerCard) {
        System.out.println("\n" + "=".repeat(50));
        System.out.println("🎯 YOUR HAND: " + formatHand(playerHand));

        if (hideDealerCard && dealerHand.size() >= 2) {
            System.out.println("🎰 DEALER'S HAND: " + dealerHand.getCards().get(0) + ", 🃏");
        } else {
            System.out.println("🎰 DEALER'S HAND: " + formatHand(dealerHand));
        }
        System.out.println("=".repeat(50));
    }

    /**
     * Format a hand for display with cards and value.
     * @param hand The hand to format
     * @return Formatted string representation
     */
    private String formatHand(Hand hand) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < hand.getCards().size(); i++) {
            if (i > 0) {
                sb.append(" ");
            }
            sb.append(hand.getCards().get(i).toString());
        }
        sb.append(" (Value: ").append(hand.getValue()).append(")");
        return sb.toString();
    }
    
    /**
     * Handle player's turn.
     * @return true if player didn't bust
     */
    private boolean playerTurn() {
        while (true) {
            showHands(true);
            
            if (playerHand.isBust()) {
                System.out.println("\n💥 You busted! You lose.");
                return false;
            }

            if (playerHand.getValue() == 21) {
                System.out.println("\n🎯 Perfect! You have 21!");
                break;
            }
            
            System.out.print("\nDo you want to (h)it or (s)tand? ");
            String action = scanner.nextLine().toLowerCase().trim();
            
            if (action.equals("h") || action.equals("hit")) {
                Card newCard = deck.dealCard();
                playerHand.addCard(newCard);
                System.out.println("📥 You drew: " + newCard);
            } else if (action.equals("s") || action.equals("stand")) {
                break;
            } else {
                System.out.println("Please enter 'h' for hit or 's' for stand.");
            }
        }
        
        return !playerHand.isBust();
    }
    
    /**
     * Handle dealer's turn.
     */
    private void dealerTurn() {
        System.out.println("\n🎰 DEALER'S TURN:");
        showHands(false);

        while (dealerHand.getValue() < 17) {
            Card card = deck.dealCard();
            dealerHand.addCard(card);
            System.out.println("🎰 Dealer draws: " + card);
            System.out.println("🎰 Dealer's hand: " + formatHand(dealerHand));
        }

        if (dealerHand.isBust()) {
            System.out.println("💥 Dealer busted!");
        }
    }
    
    /**
     * Determine the winner and return result.
     * @return String representing the game result
     */
    private String determineWinner() {
        int playerValue = playerHand.getValue();
        int dealerValue = dealerHand.getValue();
        
        // Check for blackjacks
        boolean playerBlackjack = playerHand.isBlackjack();
        boolean dealerBlackjack = dealerHand.isBlackjack();
        
        if (playerBlackjack && dealerBlackjack) {
            return "push_blackjack";
        } else if (playerBlackjack) {
            return "player_blackjack";
        } else if (dealerBlackjack) {
            return "dealer_blackjack";
        }
        
        // Check for busts
        if (playerHand.isBust()) {
            return "player_bust";
        } else if (dealerHand.isBust()) {
            return "dealer_bust";
        }
        
        // Compare values
        if (playerValue > dealerValue) {
            return "player_wins";
        } else if (dealerValue > playerValue) {
            return "dealer_wins";
        } else {
            return "push";
        }
    }
    
    /**
     * Handle betting payouts based on game result.
     * @param result The game result
     */
    private void handlePayout(String result) {
        switch (result) {
            case "player_blackjack":
                int payout = (int) (currentBet * 1.5); // 3:2 payout for blackjack
                playerBalance += payout;
                System.out.println("🎉 BLACKJACK! You win $" + payout + "! 🎉");
                break;
            case "player_wins":
            case "dealer_bust":
                playerBalance += currentBet;
                System.out.println("🎊 You win $" + currentBet + "! 🎊");
                break;
            case "push":
            case "push_blackjack":
                System.out.println("🤝 It's a push! Your bet is returned.");
                break;
            default: // Player loses
                playerBalance -= currentBet;
                System.out.println("😞 You lose $" + currentBet + ".");
                break;
        }
    }
    
    /**
     * Play one round of blackjack.
     * @return true to continue playing
     */
    private boolean playRound() {
        resetHands();
        
        // Place bet
        if (!placeBet()) {
            return false;
        }
        
        // Deal initial cards
        dealInitialCards();
        
        // Check for initial blackjacks
        if (playerHand.isBlackjack() || dealerHand.isBlackjack()) {
            showHands(false);
            String result = determineWinner();
            handlePayout(result);
        } else {
            // Player's turn
            if (playerTurn()) {
                // Dealer's turn (only if player didn't bust)
                dealerTurn();
            }
            
            // Determine winner and handle payout
            String result = determineWinner();
            handlePayout(result);
        }
        
        System.out.println("Your balance: $" + playerBalance);
        
        // Ask if player wants to continue
        if (playerBalance <= 0) {
            System.out.println("You're out of money! Thanks for playing!");
            return false;
        }
        
        while (true) {
            System.out.print("\nDo you want to play another round? (y/n): ");
            String playAgain = scanner.nextLine().toLowerCase().trim();
            if (playAgain.equals("y") || playAgain.equals("yes")) {
                return true;
            } else if (playAgain.equals("n") || playAgain.equals("no")) {
                return false;
            } else {
                System.out.println("Please enter 'y' for yes or 'n' for no.");
            }
        }
    }
    
    /**
     * Main game loop.
     */
    public void play() {
        System.out.println("🎰♠️♥️♦️♣️ WELCOME TO BLACKJACK! ♣️♦️♥️♠️🎰");
        System.out.println("🎯 Try to get as close to 21 as possible without going over.");
        System.out.println("🃏 Face cards are worth 10, Aces are worth 1 or 11.");
        System.out.println("💎 Blackjack (21 with 2 cards) pays 3:2!");
        System.out.println("💰 Starting balance: $" + playerBalance);
        System.out.println("=".repeat(60));

        while (playRound()) {
            // Continue playing rounds
        }

        System.out.println("\n🎊 Thanks for playing! Final balance: $" + playerBalance + " 🎊");
        scanner.close();
    }
    
    /**
     * Main method to start the game.
     * @param args Command line arguments (not used)
     */
    public static void main(String[] args) {
        BlackjackGame game = new BlackjackGame();
        game.play();
    }
}
