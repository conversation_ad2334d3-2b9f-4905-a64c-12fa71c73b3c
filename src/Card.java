/**
 * Represents a playing card in the blackjack game.
 */
public class Card {
    private String suit;
    private String rank;

    // ANSI color codes for terminal colors
    public static final String RESET = "\u001B[0m";
    public static final String RED = "\u001B[31m";
    public static final String BLUE = "\u001B[34m";
    public static final String BOLD = "\u001B[1m";

    public Card(String suit, String rank) {
        this.suit = suit;
        this.rank = rank;
    }

    public String getSuit() {
        return suit;
    }

    public String getRank() {
        return rank;
    }
    
    /**
     * Get the blackjack value of the card.
     * @return The card's value (Ace returns 11, will be adjusted in hand calculation)
     */
    public int getValue() {
        switch (rank) {
            case "Jack":
            case "Queen":
            case "King":
                return 10;
            case "Ace":
                return 11; // Will be adjusted in hand calculation
            default:
                return Integer.parseInt(rank);
        }
    }

    /**
     * Get the symbol representation of the suit.
     * @return Symbol for the suit
     */
    private String getSuitSymbol() {
        switch (suit) {
            case "Hearts":
                return "H";
            case "Diamonds":
                return "D";
            case "Clubs":
                return "C";
            case "Spades":
                return "S";
            default:
                return suit.substring(0, 1);
        }
    }

    /**
     * Get the color for the suit (red for Hearts/Diamonds, blue for Clubs/Spades).
     * @return ANSI color code
     */
    private String getSuitColor() {
        switch (suit) {
            case "Hearts":
            case "Diamonds":
                return RED + BOLD;
            case "Clubs":
            case "Spades":
                return BLUE + BOLD;
            default:
                return RESET;
        }
    }

    /**
     * Get a shortened rank representation for display.
     * @return Shortened rank (J, Q, K, A for face cards and Ace)
     */
    private String getDisplayRank() {
        switch (rank) {
            case "Jack":
                return "J";
            case "Queen":
                return "Q";
            case "King":
                return "K";
            case "Ace":
                return "A";
            default:
                return rank;
        }
    }

    @Override
    public String toString() {
        return getSuitColor() + getDisplayRank() + getSuitSymbol() + RESET;
    }

    /**
     * Get a detailed string representation of the card.
     * @return Full card description
     */
    public String toDetailedString() {
        return getSuitColor() + rank + " of " + suit + " (" + getSuitSymbol() + ")" + RESET;
    }
}
