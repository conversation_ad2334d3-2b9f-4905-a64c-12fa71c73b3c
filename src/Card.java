/**
 * Represents a playing card in the blackjack game.
 */
public class Card {
    private String suit;
    private String rank;
    
    public Card(String suit, String rank) {
        this.suit = suit;
        this.rank = rank;
    }
    
    public String getSuit() {
        return suit;
    }
    
    public String getRank() {
        return rank;
    }
    
    /**
     * Get the blackjack value of the card.
     * @return The card's value (Ace returns 11, will be adjusted in hand calculation)
     */
    public int getValue() {
        switch (rank) {
            case "Jack":
            case "Queen":
            case "King":
                return 10;
            case "Ace":
                return 11; // Will be adjusted in hand calculation
            default:
                return Integer.parseInt(rank);
        }
    }
    
    @Override
    public String toString() {
        return rank + " of " + suit;
    }
}
