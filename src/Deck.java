import java.util.*;

/**
 * Represents a deck of playing cards.
 */
public class Deck {
    private List<Card> cards;
    private Random random;
    
    public Deck() {
        this.cards = new ArrayList<>();
        this.random = new Random();
        createDeck();
        shuffle();
    }
    
    /**
     * Create a standard 52-card deck.
     */
    private void createDeck() {
        String[] suits = {"Hearts", "Diamonds", "Clubs", "Spades"};
        String[] ranks = {"2", "3", "4", "5", "6", "7", "8", "9", "10", "Jack", "Queen", "King", "Ace"};

        cards.clear();
        for (String suit : suits) {
            for (String rank : ranks) {
                cards.add(new Card(suit, rank));
            }
        }
    }
    
    /**
     * Shuffle the deck.
     */
    public void shuffle() {
        Collections.shuffle(cards, random);
    }
    
    /**
     * Deal one card from the deck.
     * @return A card from the deck
     */
    public Card dealCard() {
        if (cards.isEmpty()) {
            // If deck is empty, create and shuffle a new one
            createDeck();
            shuffle();
        }
        return cards.remove(cards.size() - 1);
    }
    
    /**
     * Check if the deck is empty.
     * @return true if deck is empty
     */
    public boolean isEmpty() {
        return cards.isEmpty();
    }
    
    /**
     * Get the number of cards remaining in the deck.
     * @return Number of cards left
     */
    public int size() {
        return cards.size();
    }
}
